import type { Message } from "../../messages";

export type MessagesStoreType = {
  messages: Message[];
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  removeMessage: (message: Message) => void;
  updateMessage: (message: Message) => void;
  updateMessagePartial: (message: Partial<Message>) => void;
  updateMessageText: (id: string, chunk: string) => void;
  clearMessages: () => void;
  removeMessages: (ids: string[]) => void;
  deleteSession: (id: string) => void;
  displayLoadingMessage: boolean;
};
