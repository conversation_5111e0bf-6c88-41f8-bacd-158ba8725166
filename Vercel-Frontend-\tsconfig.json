{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false, "paths": {"@/*": ["*"], "@queries/*": ["controllers/API/queries/*"]}, "baseUrl": "src"}, "include": ["src", "tests/core/saveComponents.spec.ts", "tests/extended/actionsMainPage-shard-1.spec.ts", "tests/core/auto_login.spec.ts", "tests/fe-components/chatInputOutput.spec.ts", "tests/extended/integrations/chatInputOutputUser-shard-2.spec.ts", "tests/fe-components/codeAreaModalComponent.spec.ts", "tests/extended/features/curlApiGeneration.spec.ts", "tests/extended/features/deleteFlows.spec.ts", "tests/extended/features/dragAndDrop.spec.ts", "tests/fe-components/dropdownComponent.spec.ts", "tests/fe-components/fileUploadComponent.spec.ts", "tests/core/filterEdge.spec.ts", "tests/fe-components/floatComponent.spec.ts", "tests/extended/features/flowPage.spec.ts", "tests/extended/features/flowSettings.spec.ts", "tests/extended/regression/generalBugs-shard-0.spec.ts", "tests/core/globalVariables.spec.ts", "tests/core/group.spec.ts", "tests/extended/folders.spec.ts", "tests/fe-components/inputComponent.spec.ts", "tests/core/inputListComponent.spec.ts", "tests/fe-components/intComponent.spec.ts", "tests/core/keyPairListComponent.spec.ts", "tests/extended/features/langflowShortcuts.spec.ts", "tests/core/nestedComponent.spec.ts", "tests/fe-components/promptModalComponent.spec.ts", "tests/core/python_api_generation.spec.ts", "tests/fe-components/textAreaModalComponent.spec.ts", "tests/core/textInputOutput.spec.ts", "tests/fe-components/toggleComponent.spec.ts", "tests/core/tweaks_test.spec.ts", "tests/extended/features/twoEdges.spec.ts", "tests/extended/features/userSettings.spec.ts", "tests/core/store.spec.ts", "tests/core/logs.spec.ts"]}