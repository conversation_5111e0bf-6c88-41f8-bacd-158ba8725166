@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    font-family: Inter, system-ui, sans-serif;
  }
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes gradient-motion-start {
  0% {
    stop-color: rgb(156, 138, 236);
  }
  50% {
    stop-color: rgb(255, 130, 184);
  }
  80% {
    stop-color: rgb(255, 165, 100);
  }
  100% {
    stop-color: rgb(156, 138, 236);
  }
}

@keyframes gradient-motion-end {
  0% {
    stop-color: rgb(156, 138, 236);
  }
  50% {
    stop-color: rgb(255, 165, 100);
  }
  80% {
    stop-color: rgb(255, 130, 184);
  }
  100% {
    stop-color: rgb(156, 138, 236);
  }
}

@layer components {
  .round-buttons-position {
    @apply fixed right-4;
  }
  .side-bar-arrangement {
    @apply flex h-full w-[14.5rem] flex-col overflow-hidden border-r scrollbar-hide;
  }
  .side-bar-search-div-placement {
    @apply relative mx-auto flex items-center py-3;
  }
  .side-bar-components-icon {
    @apply h-6 w-4 text-ring;
  }
  .side-bar-components-text {
    @apply w-full truncate pr-1 text-xs text-foreground;
  }
  .side-bar-components-div-form {
    @apply flex w-full items-center justify-between rounded-md rounded-l-none border border-l-0 border-dashed border-ring px-3 py-1 text-sm;
  }
  .side-bar-components-border {
    @apply cursor-grab rounded-l-md border-l-8;
  }
  .side-bar-components-gap {
    @apply flex flex-col gap-2 p-2;
  }
  .side-bar-components-div-arrangement {
    @apply w-full overflow-auto pb-10 scrollbar-hide;
  }
  .search-icon {
    @apply absolute inset-y-0 right-0 flex items-center py-1.5 pr-5;
  }
  .extra-side-bar-save-disable {
    @apply text-muted-foreground;
  }
  .side-bar-button-size {
    @apply h-5 w-5;
  }
  .side-bar-button-size:hover {
    @apply hover:text-accent-foreground;
  }
  .side-bar-buttons-arrangement {
    @apply mb-2 mt-2 flex w-full items-center justify-between gap-2 px-2;
  }
  .side-bar-button {
    @apply flex w-full;
  }
  .button-disable {
    @apply pointer-events-none;
  }
  /* Frozen state border */
  .border-ring-frozen {
    position: relative;
    @apply rounded-md border shadow-frozen-ring;
  }
  .border-ring-frozen::before {
    content: "";
    position: absolute;
    top: -2px; /* Adjust based on desired border width */
    bottom: -2px;
    left: -2px;
    right: -2px;
    /* use the frozen-blue color from tailwind */
    border-radius: inherit;
    pointer-events: none;
    @apply border-2 border-frozen-blue;
  }
  .border-frozen {
    @apply border shadow-frozen-ring;
  }
  .frozen {
    position: relative;
    overflow: hidden;
  }
  .frozen::before,
  .frozen::after {
    content: "";
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
    background: rgba(255, 255, 255, 0.5); /* Reduced opacity for better readability */
    border-radius: 10px;
    pointer-events: none;
  }
  .frozen::before {
    filter: blur(5px); /* Less blur for better readability */
  }
  .frozen::after {
    filter: blur(10px); /* Less blur for better readability */
    opacity: 0.2; /* Reduced opacity */
  }
  .extra-side-bar-buttons {
    @apply relative inline-flex w-full items-center justify-center rounded-md bg-background px-2 py-2 text-foreground transition-all duration-500 ease-in-out;
  }
  .header-menubar-item {
    @apply relative flex cursor-default cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50;
  }
  .extra-side-bar-buttons:hover {
    @apply hover:bg-muted;
  }
  .button-div-style {
    @apply flex gap-2;
  }
  .primary-input {
    @apply form-input block w-full truncate rounded-md border border-border bg-background px-3 text-left text-sm placeholder:text-muted-foreground hover:border-muted-foreground focus:border-foreground focus:placeholder-transparent focus:ring-0 focus:ring-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-muted disabled:text-muted-foreground disabled:opacity-100 placeholder:disabled:text-muted-foreground;
  }

  .skeleton-card {
    @apply flex h-48 flex-col gap-6 rounded-lg border bg-background p-4;
  }

  .skeleton-card-wrapper {
    @apply flex items-center space-x-4;
  }

  .skeleton-card-text {
    @apply flex flex-col gap-3;
  }

  /* The same as primary-input but no-truncate */
  .textarea-primary {
    @apply form-input block w-full rounded-md border border-border bg-background px-3 text-left text-sm placeholder:text-muted-foreground hover:border-muted-foreground focus:border-foreground focus:placeholder-transparent focus:ring-0 focus:ring-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-muted disabled:text-muted-foreground disabled:opacity-100 placeholder:disabled:text-muted-foreground;
  }

  .input-edit-node {
    @apply primary-input w-full pb-0.5 pt-0.5 text-left;
  }
  .input-search {
    @apply primary-input mx-2 pr-7;
  }
  .input-disable {
    @apply border-transparent bg-border placeholder:text-ring;
  }
  .input-dialog {
    @apply cursor-pointer bg-transparent text-ring;
  }
  .message-button {
    @apply message-button-position flex h-12 w-12 items-center justify-center rounded-full bg-border px-3 py-1 shadow-md transition-all;
  }

  .round-button-form {
    @apply flex h-12 w-12 cursor-pointer justify-center rounded-full bg-border px-3 py-1 shadow-md;
  }
  .build-trigger-loading-icon {
    @apply stroke-build-trigger;
  }
  .build-trigger-icon {
    @apply w-6 fill-build-trigger stroke-build-trigger stroke-1;
  }
  .message-button-position {
    @apply fixed right-4 top-20;
  }
  .message-button-icon {
    @apply fill-medium-indigo stroke-medium-indigo stroke-1;
  }
  .disabled-message-button-icon {
    @apply fill-chat-trigger-disabled stroke-chat-trigger-disabled stroke-1;
  }
  .parent-disclosure-arrangement {
    @apply flex w-full select-none items-center justify-between bg-background py-3 pl-5 pr-3;
  }
  .components-disclosure-arrangement {
    @apply -mt-px flex w-full select-none items-center justify-between border-y border-y-input bg-primary-foreground px-3 py-2;
  }
  .components-disclosure-arrangement-child {
    /* different color than the non child */
    @apply -mt-px flex w-full select-none items-center justify-between border-y border-y-input bg-primary-foreground px-3 py-2;
  }
  .components-disclosure-title {
    @apply flex items-center text-sm text-primary;
  }
  .components-disclosure-div {
    @apply flex gap-2;
  }
  .flow-page-positioning {
    @apply h-full w-full overflow-hidden;
  }
  .axie-studio-page-icon {
    @apply absolute bottom-2 left-7 flex h-6 cursor-pointer flex-col items-center justify-start overflow-hidden rounded-lg bg-foreground px-2 text-center font-sans text-xs tracking-wide text-secondary transition-all duration-500 ease-in-out;
  }

  .axie-studio-page-icon:hover {
    @apply hover:h-12;
  }

  .flex-max-width {
    @apply flex w-full;
  }

  .loading-page-panel {
    @apply flex h-full w-full items-center justify-center bg-background;
  }

  .main-page-panel {
    @apply flex-max-width h-full flex-col overflow-auto bg-background px-16;
  }

  .admin-page-panel {
    @apply flex-max-width h-full flex-col overflow-auto bg-background px-16;
  }

  .main-page-nav-arrangement {
    @apply flex-max-width justify-between py-8 pb-2;
  }

  .main-page-nav-title {
    @apply flex items-center justify-center gap-2 text-2xl font-semibold;
  }

  .main-page-nav-button {
    @apply w-4;
  }

  .main-page-description-text {
    @apply flex w-[60%] pb-4 text-muted-foreground;
  }

  .admin-page-description-text {
    @apply flex w-[80%] pb-8 text-muted-foreground;
  }

  .main-page-flows-display {
    @apply grid w-full gap-4 pb-7 md:grid-cols-2 lg:grid-cols-3;
  }

  .community-page-arrangement {
    @apply flex-max-width h-full flex-col justify-between overflow-auto bg-background px-16;
  }

  .community-page-nav-arrangement {
    @apply flex-max-width justify-between py-8 pb-2;
  }

  .community-page-nav-title {
    @apply flex items-center justify-center gap-2 text-2xl font-semibold;
  }

  .community-page-nav-button {
    @apply flex gap-2;
  }

  .community-page-description-text {
    @apply flex w-[70%] pb-8 text-muted-foreground;
  }

  .community-pages-flows-panel {
    @apply grid w-full gap-4 p-4 md:grid-cols-2 lg:grid-cols-4;
  }
  .generic-node-div {
    @apply flex flex-col justify-center bg-background transition-all;
  }
  .generic-node-validation-div {
    @apply max-h-96 overflow-auto;
  }

  .generic-node-status-position {
    @apply h-5 w-5;
  }

  .generic-node-status-animation {
    @apply hidden h-4 w-4 animate-spin rounded-full bg-ring opacity-0;
  }
  .generic-node-status {
    @apply animate-wiggle opacity-100;
  }
  .green-status {
    @apply generic-node-status text-status-green;
  }
  .gray-status {
    @apply generic-node-status text-status-gray;
  }

  .red-status {
    @apply generic-node-status text-status-red;
  }
  .yellow-status {
    @apply generic-node-status text-status-yellow;
  }
  .inactive-status {
    /* what colour for inactive status?
    muted-foreground is too strong, maybe use a lighter shade of it?

     */
    @apply border-none ring grayscale;
  }
  .built-invalid-status {
    @apply border-none ring ring-[#FF9090];
  }
  .built-invalid-status-dark {
    @apply border-none ring ring-[#751C1C];
  }
  .building-status {
    @apply border-blue-400;
  }
  .status-build-animation {
    @apply opacity-0;
  }
  .status-div {
    @apply absolute w-4 duration-200 ease-in-out;
  }
  .status-div:hover {
    @apply hover:text-accent-foreground hover:transition-all;
  }
  .generic-node-desc {
    @apply h-full w-full px-5 pb-4 text-foreground;
  }
  .generic-node-desc-text {
    @apply w-full text-sm text-muted-foreground;
  }

  .alert-icon {
    @apply h-5 w-5;
  }
  .alert-font-size {
    @apply text-sm font-medium;
  }

  .error-build-message {
    @apply mt-6 w-96 rounded-md bg-error-background p-4 shadow-xl;
  }
  .error-build-message-circle {
    @apply alert-icon text-status-red;
  }
  .error-build-text {
    @apply text-error-foreground word-break-break-word;
  }
  .error-build-foreground {
    @apply error-build-text alert-font-size;
  }
  .error-build-message-div {
    @apply error-build-text mt-2 text-sm;
  }
  .error-build-message-list {
    @apply list-disc space-y-1 pl-5;
  }

  .success-alert {
    @apply mt-6 w-96 rounded-md bg-success-background p-4 shadow-xl;
  }
  .success-alert-icon {
    @apply alert-icon text-status-green;
  }
  .success-alert-message {
    @apply alert-font-size text-success-foreground word-break-break-word;
  }

  .unused-side-bar-aside {
    @apply flex flex-shrink-0 flex-col overflow-hidden border-r transition-all duration-500;
  }
  .unused-side-bar-arrangement {
    @apply flex h-full w-52 flex-col items-start overflow-y-auto border bg-background scrollbar-hide;
  }
  .unused-side-bar-division {
    @apply flex-max-width flex-grow flex-col;
  }
  .unused-side-bar-nav {
    @apply flex-1 space-y-1;
  }
  .unused-side-bar-link {
    @apply flex-max-width items-center rounded-md py-2 pl-2 text-sm font-medium;
  }
  .unused-side-bar-link-colors-true {
    @apply bg-muted text-foreground;
  }
  .unused-side-bar-link-colors-false {
    @apply bg-background text-muted-foreground hover:bg-muted hover:text-foreground;
  }
  .unused-side-bar-icon {
    @apply mr-3 h-6 w-6 flex-shrink-0;
  }
  .unused-side-bar-icon-false {
    @apply text-ring group-hover:text-accent-foreground;
  }
  .unused-side-bar-disclosure {
    @apply unused-side-bar-link pr-1 text-left;
  }
  .unused-side-bar-disclosure:focus {
    @apply focus:outline-none focus:ring-1 focus:ring-ring;
  }
  .unused-side-bar-disclosure-icon {
    @apply unused-side-bar-icon text-ring group-hover:text-accent-foreground;
  }
  .unused-side-bar-svg-true {
    @apply rotate-90 text-ring;
  }
  .unused-side-bar-svg {
    @apply ml-3 h-5 w-5 flex-shrink-0 duration-150 ease-in-out group-hover:text-accent-foreground;
  }
  .unused-side-bar-disclosure-panel {
    @apply flex w-full items-center rounded-md py-2 pl-11 pr-2 text-sm font-medium;
  }

  .code-area-component {
    @apply pointer-events-none w-full cursor-not-allowed;
  }
  .code-area-input-positioning {
    @apply flex-max-width items-center;
  }
  .code-area-external-link {
    @apply ml-3 h-6 w-6;
  }
  .code-area-external-link:hover {
    @apply hover:text-accent-foreground;
  }
  .dropdown-component-disabled {
    @apply pointer-events-none cursor-not-allowed;
  }
  .dropdown-component-outline {
    @apply input-edit-node relative pr-8;
  }
  .dropdown-component-false-outline {
    @apply primary-input py-2 pl-3 pr-10 text-left;
  }
  .dropdown-component-display {
    @apply block w-full truncate bg-background;
  }
  .dropdown-component-display-disabled {
    @apply text-muted-foreground;
  }
  .dropdown-component-arrow {
    @apply pointer-events-none absolute inset-y-0 flex items-center pr-2;
  }
  .dropdown-component-arrow-color {
    @apply h-5 w-5 text-accent-foreground;
  }
  .dropdown-component-arrow-color-disable {
    @apply h-5 w-5 text-muted-foreground;
  }
  .dropdown-component-options {
    @apply z-10 mt-1 max-h-60 overflow-auto rounded-md bg-background py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm;
  }
  .dropdown-component-true-options {
    @apply dropdown-component-options lg:w-[32%];
  }
  .dropdown-component-false-options {
    @apply dropdown-component-options w-full;
  }
  .dropdown-component-option {
    @apply relative cursor-default select-none;
  }
  .dropdown-component-false-option {
    @apply dropdown-component-option py-0.5 pl-3 pr-12;
  }
  .dropdown-component-true-option {
    @apply dropdown-component-option py-2 pl-3 pr-9;
  }
  .dropdown-component-choosal {
    @apply absolute inset-y-0 right-0 flex items-center pr-4;
  }
  .dropdown-component-check-icon {
    @apply h-5 w-5 text-primary;
  }

  .multiselect-component-outline {
    @apply input-edit-node relative pr-8;
  }
  .multiselect-component-false-outline {
    @apply primary-input py-2 pl-3 pr-10 text-left;
  }

  .edit-flow-arrangement {
    @apply flex justify-between;
  }
  .edit-flow-span {
    @apply ml-8 text-mmd font-normal text-status-red;
  }

  .float-component-pointer {
    @apply pointer-events-none cursor-not-allowed;
  }

  .header-menu-bar {
    @apply flex items-center rounded-md py-1 text-sm font-medium;
  }

  .header-menu-bar-display {
    @apply flex max-w-[110px] cursor-pointer items-center gap-2 lg:max-w-[150px];
  }

  .header-menu-flow-name {
    @apply flex-1 truncate;
  }
  .header-menu-options {
    @apply mr-2 h-4 w-4;
  }

  .header-arrangement {
    @apply flex-max-width h-[53px] items-center justify-between border-b border-border;
  }

  .header-start-display {
    @apply flex items-center gap-2;
  }
  .header-end-division {
    @apply flex justify-end px-2;
  }
  .header-end-display {
    @apply ml-auto mr-2 flex items-center gap-5;
  }
  .header-github-link-box {
    @apply inline-flex h-8 items-center justify-center rounded-md border border-input px-2 pr-0;
  }

  .header-waitlist-link-box {
    @apply inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border border-input px-2 text-sm font-medium text-muted-foreground shadow-sm ring-offset-background disabled:pointer-events-none disabled:opacity-50;
  }
  .header-waitlist-link-box:hover {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  .header-github-link {
    @apply header-github-link-box text-sm font-medium text-muted-foreground ring-offset-background disabled:pointer-events-none disabled:opacity-50;
  }
  .header-github-link:focus-visible {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
  .header-github-link:hover {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  .header-notifications-box {
    @apply fixed left-0 top-0 h-screen w-screen;
  }
  .header-notifications {
    @apply absolute right-[3px] h-1.5 w-1.5 rounded-full bg-destructive;
  }
  .input-component-div {
    @apply pointer-events-none relative cursor-not-allowed;
  }
  .input-component-button {
    @apply absolute inset-y-0 right-0 items-center text-muted-foreground;
  }
  .input-component-true-button {
    @apply input-component-button pr-2;
  }
  .input-component-false-button {
    @apply input-component-button px-4;
  }
  .input-component-true-svg {
    @apply side-bar-button-size absolute bottom-0.5 right-2;
  }
  .input-component-false-svg {
    @apply side-bar-button-size absolute bottom-2 right-3;
  }

  .input-file-component {
    @apply flex-max-width items-center;
  }

  .toggle-component-switch {
    @apply relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out;
  }
  .toggle-component-switch:focus {
    @apply focus:outline-none focus:ring-1 focus:ring-primary focus:ring-offset-1;
  }
  .toggle-component-span {
    @apply pointer-events-none relative h-5 w-5 transform rounded-full shadow ring-0 transition duration-200 ease-in-out;
  }
  .toggle-component-second-span {
    @apply absolute inset-0 flex h-full w-full items-center justify-center transition-opacity;
  }

  .app-div {
    @apply absolute bottom-5 left-5 flex flex-col-reverse;
  }

  .chat-input-modal-txtarea {
    @apply form-input block w-full rounded-md border-ring pr-10 custom-scroll sm:text-sm;
  }
  .chat-input-modal-div {
    @apply absolute bottom-0.5 right-3;
  }
  .chat-input-modal-lock {
    @apply side-bar-button-size animate-pulse text-ring;
  }
  .chat-input-modal-send {
    @apply side-bar-button-size text-ring hover:text-muted-foreground;
  }

  .code-block-modal {
    @apply flex items-center justify-between px-4 py-1.5;
  }
  .code-block-modal-span {
    @apply text-xs lowercase text-muted-foreground;
  }
  .code-block-modal-button {
    @apply flex items-center gap-1.5 rounded bg-none p-1 text-xs text-muted-foreground;
  }

  .chat-message-modal {
    @apply flex-max-width py-2 pl-2;
  }
  .chat-message-modal-div {
    @apply my-3 flex h-8 w-8 items-center justify-center overflow-hidden rounded-full;
  }
  .chat-message-modal-img {
    @apply absolute scale-150 transition-opacity duration-500;
  }
  .chat-message-modal-display {
    @apply flex-max-width items-center text-start;
  }
  .chat-message-modal-text {
    @apply relative w-full text-start text-sm font-normal text-muted-foreground;
  }
  .chat-message-modal-icon-div {
    @apply absolute -left-2 -top-1 cursor-pointer;
  }
  .chat-message-modal-thought {
    @apply chat-message-modal-thought-cursor ml-3 h-full w-[95%] rounded-md border border-ring bg-muted px-2 pb-3 pt-3 text-start text-muted-foreground;
  }
  .chat-message-modal-thought-cursor {
    @apply cursor-pointer overflow-scroll scrollbar-hide;
  }
  .chat-message-modal-markdown {
    @apply w-full px-4 pb-3 pr-8 pt-3;
  }
  .chat-message-modal-markdown-span {
    @apply mt-1 animate-pulse cursor-default;
  }
  .chat-message-modal-alert {
    @apply px-3 text-start text-muted-foreground;
  }

  .file-card-modal-image-div {
    @apply absolute right-0 top-0 rounded-bl-lg bg-muted px-1 text-sm font-bold text-foreground;
  }
  .file-card-modal-image-button {
    @apply px-2 py-1 text-ring;
  }
  .file-card-modal-button {
    @apply flex w-1/2 items-center justify-between rounded border border-ring bg-muted px-2 py-2 text-foreground shadow hover:drop-shadow-lg;
  }
  .file-card-modal-div {
    @apply flex-max-width mr-2 items-center gap-2 text-current;
  }
  .file-card-modal-footer {
    @apply flex flex-col items-start;
  }
  .file-card-modal-name {
    @apply truncate text-sm text-current;
  }
  .file-card-modal-type {
    @apply truncate text-xs text-ring;
  }

  .send-message-modal-transition {
    @apply fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm transition-opacity;
  }
  .chat-modal-box {
    @apply fixed inset-0 z-10 overflow-y-auto;
  }
  .chat-modal-box-div {
    @apply flex h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
  }
  .chat-modal-dialog-panel {
    @apply relative flex h-[95%] w-[690px] transform flex-col justify-between overflow-hidden rounded-lg bg-background text-left shadow-xl drop-shadow-2xl transition-all;
  }
  .chat-modal-dialog-panel-div {
    @apply relative w-full p-4;
  }
  .chat-modal-dialog-trash-panel {
    @apply absolute right-10 top-2 z-30 text-muted-foreground hover:text-status-red;
  }
  .chat-modal-dialog-x-panel {
    @apply absolute right-2 top-1.5 z-30 text-muted-foreground hover:text-status-red;
  }
  .chat-modal-dialog-history {
    @apply flex-max-width h-full flex-col items-center overflow-scroll border-t bg-background scrollbar-hide;
  }
  .chat-modal-dialog-span-box {
    @apply flex-max-width h-full flex-col items-center justify-center text-center align-middle;
  }
  .chat-modal-dialog-desc {
    @apply w-2/4 rounded-md border border-input bg-muted px-6 py-8;
  }
  .chat-modal-input-div {
    @apply flex-max-width flex-col items-center justify-between border-t bg-background p-3;
  }
  .chat-modal-input {
    @apply relative mt-1 w-full rounded-md shadow-sm;
  }
  .code-area-modal-editor-div {
    @apply flex-max-width mt-2 h-full;
  }
  .code-area-modal-editor-box {
    @apply h-[300px] w-full rounded-lg border border-ring custom-scroll;
  }

  .edit-node-modal-variable {
    @apply h-5 w-5 stroke-2 pe-1 text-muted-foreground;
  }
  .edit-node-modal-span {
    @apply text-sm font-semibold text-primary;
  }
  .edit-node-modal-arrangement {
    @apply flex-max-width h-fit max-h-[400px];
  }
  .edit-node-modal-box {
    @apply w-full rounded-lg border border-input bg-background;
  }
  .edit-node-modal-table {
    @apply flex h-fit flex-col gap-5;
  }
  .edit-node-modal-table-header {
    @apply h-10 border-input text-xs font-medium text-ring;
  }
  .edit-node-modal-table-cell {
    @apply truncate p-0 text-center text-sm text-foreground sm:px-3;
  }
  .edit-node-modal-second-cell {
    @apply w-[300px] p-0 text-center text-xs text-foreground;
  }

  .generic-modal-txtarea-div {
    @apply flex-max-width mt-2 h-full;
  }

  .dialog-header-modal-div {
    @apply absolute left-0 top-2 z-50 hidden pl-4 pt-4 sm:block;
  }
  .dialog-header-modal-button {
    @apply rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .dialog-modal-examples-div {
    @apply h-full w-full overflow-y-auto scrollbar-hide;
  }
  .dialog-modal-example-true {
    @apply mx-auto flex flex-row flex-wrap items-start justify-center overflow-auto;
  }
  .dialog-modal-example-false {
    @apply flex flex-row items-center justify-center;
  }
  .dialog-modal-button-box-div {
    @apply flex-max-width h-full items-center justify-evenly;
  }
  .document-icon {
    @apply h-10 w-10 flex-shrink-0;
  }
  .loading-component-div {
    @apply flex items-center justify-center align-middle;
  }
  .dialog-modal-footer {
    @apply flex-max-width mt-2 items-center justify-center;
  }
  .dialog-modal-footer-link {
    @apply flex items-center justify-center text-muted-foreground;
  }

  .node-modal-div {
    @apply fixed inset-0 bg-ring bg-opacity-75 transition-opacity;
  }
  .node-modal-dialog-arrangement {
    @apply fixed inset-0 z-10 overflow-y-auto;
  }
  .node-modal-dialog-div {
    @apply flex h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
  }
  .node-modal-dialog-panel {
    @apply relative flex h-[600px] w-[700px] transform flex-col justify-between overflow-hidden rounded-lg bg-background text-left shadow-xl transition-all sm:my-8;
  }
  .node-modal-dialog-panel-div {
    @apply absolute right-0 top-0 z-50 hidden pr-4 pt-4 sm:block;
  }
  .node-modal-dialog-button {
    @apply rounded-md text-ring hover:text-accent-foreground;
  }
  .node-modal-dialog-icon-div {
    @apply flex-max-width h-full flex-col items-center justify-center;
  }
  .node-modal-icon-arrangement {
    @apply flex-max-width z-10 justify-center pb-4 shadow-sm;
  }
  .node-modal-icon {
    @apply mt-4 h-10 w-10 rounded p-1;
  }
  .node-modal-title-div {
    @apply mt-4 text-center sm:ml-4 sm:text-left;
  }
  .node-modal-title {
    @apply text-lg font-medium leading-10 text-foreground;
  }
  .node-modal-template-div {
    @apply flex-max-width h-full flex-row items-center justify-center gap-4 bg-input p-4;
  }
  .node-modal-template {
    @apply w-full rounded-lg bg-background px-4 shadow sm:p-4;
  }
  .node-modal-template-column {
    @apply flex h-full flex-col gap-5;
  }
  .node-modal-button-box {
    @apply flex-max-width flex-row-reverse bg-input px-4 pb-3;
  }
  .link-color {
    @apply font-semibold text-foreground;
  }
  .node-modal-button {
    @apply inline-flex w-full justify-center rounded-md border border-transparent bg-status-red px-4 py-2 text-base font-medium text-background shadow-sm hover:bg-ring sm:ml-3 sm:w-auto sm:text-sm;
  }
  .node-modal-button:focus {
    @apply focus:outline-none focus:ring-1 focus:ring-ring focus:ring-offset-1;
  }

  .prompt-modal-icon-box {
    @apply mx-auto mt-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-almost-light-blue sm:mx-0 sm:h-10 sm:w-10;
  }
  .prompt-modal-icon {
    @apply h-6 w-6 text-primary;
  }
  .prompt-modal-txtarea-arrangement {
    @apply flex-max-width h-full flex-row items-center justify-center gap-4 overflow-auto bg-accent p-4;
  }
  .prompt-modal-txtarea-box {
    @apply h-full w-full overflow-hidden rounded-lg bg-background px-4 py-5 shadow sm:p-6;
  }
  .prompt-modal-txtarea {
    @apply form-input h-full w-full rounded-lg border-ring;
  }

  .txtarea-modal-arrangement {
    @apply flex h-full w-full flex-row items-center justify-center gap-4 bg-input p-4;
  }
  .txtarea-modal-box {
    @apply w-full overflow-hidden rounded-lg bg-background px-4 py-5 shadow sm:p-6;
  }
  .txtarea-modal-input {
    @apply form-input h-full w-full;
  }

  .api-modal-tabs {
    @apply flex h-full flex-col overflow-hidden;
  }
  .api-modal-tablist-div {
    @apply flex items-center justify-between px-2 py-2;
  }
  .api-modal-tabs-content {
    @apply h-full w-full;
  }
  .api-modal-accordion-display {
    @apply mt-2 flex h-full w-full;
  }
  .api-modal-table-arrangement {
    @apply flex h-fit flex-col gap-5;
  }

  .icons-parameters-comp {
    @apply h-6 w-6;
  }

  .form-modal-lock-true {
    @apply bg-input text-primary;
  }
  .form-modal-no-input {
    @apply bg-input text-center text-primary dark:bg-gray-700 dark:text-gray-300;
  }
  .form-modal-lock-false {
    @apply text-primary;
  }
  .code-highlight {
    @apply block w-full overflow-auto border-0 px-3 py-2 text-sm outline-0 word-break-break-word;
  }

  .code-nohighlight {
    @apply block w-full overflow-auto border-0 px-3 py-2 text-sm outline-0 word-break-break-word;
  }
  .form-modal-lockchat {
    @apply form-input block w-full rounded-md border border-border p-4 pr-16 custom-scroll focus:border-ring focus:ring-0 sm:text-sm;
  }
  .form-modal-send-icon-position {
    @apply absolute bottom-2 right-4;
  }
  .form-modal-send-button {
    @apply rounded-md p-1.5 px-2.5;
  }
  .form-modal-lock-icon {
    @apply ml-1 mr-1 h-5 w-5 animate-pulse;
  }
  .form-modal-send-icon {
    @apply mr-2 h-5 w-5 rotate-[44deg];
  }
  .form-modal-play-icon {
    @apply mx-1 h-5 w-5 fill-current;
  }
  .form-modal-chat-position {
    @apply flex-max-width px-2 py-6 pl-4 pr-9;
  }
  .form-modal-chatbot-icon {
    @apply mb-3 ml-3 mr-6 mt-1 flex flex-col;
  }
  .form-modal-chat-image {
    @apply flex flex-col items-center gap-1;
  }
  .form-modal-chat-img-box {
    @apply relative flex h-8 w-8 items-center justify-center overflow-hidden rounded-md p-5 text-2xl;
  }
  .form-modal-chat-bot-icon {
    @apply form-modal-chat-img-box bg-chat-bot-icon;
  }
  .form-modal-chat-user-icon {
    @apply form-modal-chat-img-box bg-chat-user-icon;
  }
  .form-modal-chat-icon-img {
    @apply absolute scale-[60%];
  }
  .form-modal-chat-text-position {
    @apply flex w-full flex-1 text-start;
  }
  .form-modal-chat-text {
    @apply relative flex w-full flex-col text-start text-sm font-normal text-muted-foreground;
  }
  .form-modal-chat-icon-div {
    @apply absolute -left-6 -top-3 cursor-pointer;
  }
  .form-modal-chat-icon {
    @apply h-4 w-4 animate-bounce;
  }
  .form-modal-chat-thought-border {
    @apply rounded-md border border-ring/60;
  }
  .form-modal-chat-thought-size {
    @apply h-full w-[95%];
  }
  .form-modal-chat-thought {
    @apply form-modal-chat-thought-border form-modal-chat-thought-size cursor-pointer overflow-scroll bg-background px-2 py-2 text-start text-primary scrollbar-hide;
  }
  .form-modal-markdown-span {
    @apply mt-1 animate-pulse cursor-default;
  }
  .form-modal-initial-prompt-btn {
    @apply mb-2 flex items-center gap-2 rounded-md border border-border bg-background px-4 py-2 text-sm font-semibold shadow-sm;
  }
  .form-modal-iv-size {
    @apply mr-6 flex h-full w-2/6 flex-col justify-start overflow-auto scrollbar-hide;
  }
  .file-component-arrangement {
    @apply flex items-center py-2;
  }
  .file-component-variable {
    @apply -ml-px mr-1 h-4 w-4 text-primary;
  }
  .file-component-variables-span {
    @apply font-semibold text-primary;
  }
  .file-component-variables-title {
    @apply flex items-center justify-between pt-2;
  }
  .file-component-variables-div {
    @apply mr-2.5 flex items-center;
  }
  .file-component-variables-title-txt {
    @apply text-sm font-medium text-primary;
  }
  .file-component-accordion-div {
    @apply flex items-start gap-3;
  }
  .file-component-badge-div {
    @apply flex-max-width items-center justify-between;
  }
  .file-component-tab-column {
    @apply flex flex-col gap-2 p-1;
  }
  .tab-accordion-badge-div {
    @apply flex flex-1 items-center justify-between py-4 text-sm font-normal text-muted-foreground transition-all;
  }
  .eraser-column-arrangement {
    @apply flex-max-width flex-1 flex-col;
  }
  .eraser-size {
    @apply relative flex h-full w-full flex-col rounded-md border bg-muted;
  }
  .eraser-position {
    @apply absolute right-3 top-3 z-50;
  }
  .chat-message-div {
    @apply flex-max-width h-full flex-col items-center overflow-scroll scrollbar-hide;
  }
  .chat-alert-box {
    @apply flex-max-width h-full flex-col items-center justify-center text-center align-middle;
  }
  .axie-studio-chat-span {
    @apply text-lg text-foreground;
  }
  .card-filter {
    @apply bg-background bg-fixed opacity-20;
  }
  .axie-studio-chat-desc {
    @apply w-2/4 rounded-md border border-border bg-muted px-6 py-8;
  }
  .axie-studio-chat-desc-span {
    @apply text-base text-muted-foreground;
  }
  .axie-studio-chat-input-div {
    @apply flex-max-width flex-col items-center justify-between px-8 pb-3;
  }
  .axie-studio-chat-input {
    @apply relative w-full rounded-md shadow-sm;
  }

  .tooltip-fixed-width {
    @apply max-h-[25vh] max-w-[30vw] overflow-auto;
  }

  .ace-editor-arrangement {
    @apply flex-max-width h-full flex-col transition-all;
  }
  .ace-editor {
    @apply h-full w-full rounded-lg border border-border custom-scroll;
  }
  .ace-editor-save-btn {
    @apply flex-max-width h-fit justify-end;
  }

  .export-modal-save-api {
    @apply font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .beta-badge-wrapper {
    @apply pointer-events-none absolute right-0 top-0 h-16 w-16 overflow-hidden rounded-tr-lg;
  }
  .beta-badge-content {
    @apply mt-2 w-24 rotate-45 bg-beta-background text-center text-xs font-semibold text-beta-foreground;
  }

  .chat-message-highlight {
    @apply rounded-md bg-indigo-100 px-0 font-semibold dark:bg-indigo-900;
  }

  .field-invalid {
    @apply absolute text-[0.8rem] font-medium text-destructive;
  }

  .label-invalid {
    @apply text-destructive;
  }

  .input-invalid {
    @apply border-destructive focus:border-destructive focus:ring-destructive;
  }

  .fade-container {
    @apply relative overflow-hidden;
  }

  .fade-container::before,
  .fade-container::after {
    @apply pointer-events-none absolute bottom-0 top-0 bg-gradient-to-r from-background to-transparent;
    content: "";
    width: 50px;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .fade-container::after {
    @apply right-0;
    transform: rotate(180deg);
  }

  .fade-container.fade-left::before,
  .fade-container.fade-right::after {
    opacity: 1;
  }

  .fade-container-dark {
    @apply relative overflow-hidden;
  }

  .fade-container-dark::before,
  .fade-container-dark::after {
    @apply pointer-events-none absolute bottom-0 top-0;
    content: "";
    width: 50px;
    opacity: 0;
    transition: opacity 0.3s;
    background: linear-gradient(to right, black, transparent);
  }

  .fade-container-dark:after {
    @apply right-0;
    transform: rotate(180deg);
  }

  .fade-container-dark.fade-left::before,
  .fade-container-dark.fade-right::after {
    opacity: 1;
  }

  .scroll-container {
    @apply flex overflow-x-scroll scrollbar-hide;
  }

  .store-beta-icon {
    @apply relative bottom-3 left-1 ml-2 rounded-full bg-beta-background px-2 py-1 text-center text-xs font-semibold text-beta-foreground;
  }
  .color-option-container {
    @apply w-fit;
  }

  .x-gradient {
    @apply stroke-[url(#x-gradient)] bg-blend-hard-light;
  }

  .button-run-bg {
    @apply flex h-6 w-6 cursor-pointer items-center justify-center rounded-sm bg-transparent hover:bg-muted;
  }

  .show-node-icon {
    @apply absolute inset-x-6;
  }

  .disabled-state {
    @apply pointer-events-none bg-secondary text-input;
  }

  .background-fade-input {
    @apply absolute right-[0.9px] h-6 w-7 bg-background;
  }

  .gradient-fade-input {
    @apply absolute right-7 h-6 w-12 rounded-l-xl;
  }

  .background-fade-input-edit-node {
    @apply absolute right-[0.9px] h-4 w-7 bg-background;
  }

  .gradient-fade-input-edit-node {
    @apply absolute right-7 h-4 w-12 rounded-l-xl bg-background;
  }

  .fade-top-position {
    @apply top-[-35px];
  }
  .icon-top-position {
    @apply top-[-30px];
  }
  .edit-node-icon-top-position {
    @apply top-[-23px];
  }
  .popover-input {
    @apply h-fit w-fit flex-1 border-none bg-transparent p-0 shadow-none outline-none ring-0 ring-offset-0 placeholder:text-muted-foreground focus:border-foreground focus:outline-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .beta-badge {
    @apply flex h-6 w-11 items-center justify-center rounded-lg bg-accent-pink p-2 text-center text-xs font-medium text-accent-pink-foreground;
  }

  .icon-size {
    @apply h-[18px] w-[18px];
  }

  .hit-area-icon {
    @apply h-7 w-7 rounded-md;
  }

  .hit-area-hover {
    @apply transition-colors duration-200 hover:bg-muted hover:text-foreground;
  }

  .node-toolbar-buttons {
    @apply flex w-max items-center gap-1 rounded-lg text-foreground;
  }

  .last-output-border {
    @apply rounded-b-[12.5px];
  }

  .toolbar-wrapper {
    @apply flex h-10 items-center gap-1 rounded-xl border border-border bg-background p-1 shadow-sm;
  }

  .playground-btn-flow-toolbar {
    @apply relative inline-flex h-8 w-full items-center justify-center gap-1.5 rounded px-3 py-1.5 text-sm font-normal transition-all duration-500 ease-in-out;
  }

  .input-slider-text {
    @apply absolute right-3 w-14 -translate-y-3.5 cursor-text rounded-sm px-2 py-[1px] text-center hover:ring-[1px] hover:ring-slider-input-border;
  }

  .cursor-pointer,
  .cursor-default,
  .cursor-not-allowed,
  label,
  button {
    @apply select-none;
  }

  .btn-add-input-list {
    @apply flex h-6 w-full items-center justify-center rounded-md p-2 text-sm hover:bg-muted;
  }

  .btn-playground-actions {
    @apply flex h-[32px] w-[32px] items-center justify-center rounded-md bg-muted font-bold transition-all;
  }

  .no-focus-visible {
    @apply focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0;
    --tw-ring-offset-width: none !important;
    --tw-ring-shadow: none !important;
    --tw-ring-offset-shadow: none !important;
    --tw-ring-color: none !important;
  }

  .helper-lines {
    @apply pointer-events-none absolute left-0 top-0 z-10 h-full w-full;
  }

  .helper-line {
    stroke: hsl(var(--primary));
    stroke-width: 1;
    stroke-dasharray: 4 4;
    opacity: 0.8;
    filter: drop-shadow(0 0 2px hsl(var(--primary) / 0.3));
  }

  .helper-line.horizontal {
    stroke: hsl(var(--primary));
  }

  .helper-line.vertical {
    stroke: hsl(var(--primary));
  }
}

/* Gradient background */
.text-container {
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  justify-content: center;
  align-items: center;
}

.deploy-dropdown-item {
  @apply cursor-pointer rounded-none px-3 py-2 text-[13px] font-normal;
}

:root {
  --color-bg1: rgb(255, 255, 255);
  --color1: 255, 50, 118;
  --color2: 244, 128, 255;
  --color3: 117, 40, 252;
  --color-interactive: 140, 100, 255;
  --circle-size: 50%;
  --blending: hard-light;
}

.dark {
  --color-bg1: rgb(0, 0, 0);
  --color1: 255, 50, 118;
  --color2: 244, 128, 255;
  --color3: 117, 40, 252;
  --color-interactive: 140, 100, 255;
  --circle-size: 60%;
  --blending: hard-light;
}

@keyframes moveInCircle {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.gradient-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  background:
    url(data:image/png;base64,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),
    var(--color-bg1);
  top: 0;
  left: 0;
  background-blend-mode: overlay;

  svg {
    display: none;
  }

  .gradients-container {
    filter: url(#lf-balls) blur(40px);
    width: 100%;
    height: 100%;
  }

  .g1 {
    position: absolute;
    background: radial-gradient(
        circle at center,
        rgba(var(--color1), 0.8) 0,
        rgba(var(--color1), 0) 50%
      )
      no-repeat;
    mix-blend-mode: var(--blending);
    width: var(--circle-size);
    height: var(--circle-size);
    top: 10%;
    right: 20%;
    transform-origin: 50% 100%;
    animation: moveInCircle 10s linear infinite;
    opacity: 1;
  }

  .g2 {
    position: absolute;
    background: radial-gradient(
        circle at center,
        rgba(var(--color2), 0.8) 0,
        rgba(var(--color2), 0) 50%
      )
      no-repeat;
    mix-blend-mode: var(--blending);

    width: var(--circle-size);
    height: var(--circle-size);
    top: 10%;
    left: 10%;

    transform-origin: 50% 100%;
    animation: moveInCircle 12s linear infinite;

    opacity: 1;
  }

  .g3 {
    position: absolute;
    background: radial-gradient(
        circle at center,
        rgba(var(--color3), 0.8) 0,
        rgba(var(--color3), 0) 50%
      )
      no-repeat;
    mix-blend-mode: var(--blending);

    width: var(--circle-size);
    height: var(--circle-size);
    top: 10%;
    right: 30%;

    transform-origin: 50% 100%;
    animation: moveInCircle 11s linear infinite;

    opacity: 1;
  }

  .g4 {
    position: absolute;
    background: radial-gradient(
        circle at center,
        rgba(var(--color1), 0.8) 0,
        rgba(var(--color1), 0) 50%
      )
      no-repeat;
    mix-blend-mode: var(--blending);

    width: var(--circle-size);
    height: var(--circle-size);
    top: 5%;
    right: 50%;

    transform-origin: 50% 20%;
    animation: moveInCircle 12s reverse linear infinite;

    opacity: 0.7;
  }

  .g5 {
    position: absolute;
    background: radial-gradient(
        circle at center,
        rgba(var(--color2), 0.8) 0,
        rgba(var(--color2), 0) 50%
      )
      no-repeat;
    mix-blend-mode: var(--blending);

    width: var(--circle-size);
    height: var(--circle-size);
    top: 10%;
    left: 30%;

    transform-origin: 50% 20%;
    animation: moveInCircle 11s reverse linear infinite;
    opacity: 1;
  }

  .g6 {
    position: absolute;
    background: radial-gradient(
        circle at center,
        rgba(var(--color3), 0.8) 0,
        rgba(var(--color3), 0) 50%
      )
      no-repeat;
    mix-blend-mode: var(--blending);

    width: var(--circle-size);
    height: var(--circle-size);
    top: 10%;
    right: 10%;

    transform-origin: 50% 20%;
    animation: moveInCircle 10s reverse linear infinite;

    opacity: 1;
  }
}
