const SvgVertexAi = (props) => (
  <svg viewBox="0 0 32 32" width="1em" height="1em" {...props}>
    <path
      fill="#80868b"
      d="M26.69 18.53a1 1 0 0 0-1.4-.22L16 25.17v.29a1 1 0 1 1 0 1.91v.05a1 1 0 0 0 .6-.19l9.88-7.3a1 1 0 0 0 .21-1.4z"
    />
    <path
      fill="#9ba0a5"
      d="M16 27.37a1 1 0 1 1 0-1.91v-.29l-9.29-6.86a1 1 0 0 0-1.4.22 1 1 0 0 0 .21 1.4l9.89 7.3a1 1 0 0 0 .59.19v-.05z"
    />
    <path
      fill="#606368"
      d="M16 24.46a2 2 0 1 0 2 2 2 2 0 0 0-2-2zm0 2.91a1 1 0 1 1 1-.95 1 1 0 0 1-1 .95z"
    />
    <path
      fill="#9ba0a5"
      d="M8 8.14a1 1 0 0 1-1-1V4.63a1 1 0 1 1 2 0v2.51a1 1 0 0 1-1 1z"
    />
    <circle cx={7.97} cy={16} r={1.01} fill="#9ba0a5" />
    <circle cx={7.97} cy={13.05} r={1.01} fill="#9ba0a5" />
    <circle cx={7.97} cy={10.09} r={1.01} fill="#9ba0a5" />
    <path
      fill="#606368"
      d="M24 11.07a1 1 0 0 1-1-1V7.55a1 1 0 0 1 2 0v2.52a1 1 0 0 1-1 1z"
    />
    <circle cx={24.03} cy={16.01} r={1.01} fill="#606368" />
    <circle cx={24.03} cy={13.02} r={1.01} fill="#606368" />
    <circle cx={24.03} cy={4.63} r={1.01} fill="#606368" />
    <path
      fill="#80868b"
      d="M16 20a1 1 0 0 1-1-1v-2.54a1 1 0 0 1 2 0V19a1 1 0 0 1-1 1z"
    />
    <circle cx={16} cy={21.93} r={1.01} fill="#80868b" />
    <circle cx={16} cy={13.51} r={1.01} fill="#80868b" />
    <circle cx={16} cy={10.56} r={1.01} fill="#80868b" />
    <path
      fill="#606368"
      d="M20 14.05a1 1 0 0 1-1-1v-2.51a1 1 0 1 1 2 0v2.51a1 1 0 0 1-1 1z"
    />
    <circle cx={20.02} cy={7.58} r={1.01} fill="#606368" />
    <circle cx={20.02} cy={18.92} r={1.01} fill="#606368" />
    <circle cx={20.02} cy={15.97} r={1.01} fill="#606368" />
    <circle cx={11.98} cy={18.92} r={1.01} fill="#9ba0a5" />
    <circle cx={11.98} cy={10.56} r={1.01} fill="#9ba0a5" />
    <circle cx={11.98} cy={7.58} r={1.01} fill="#9ba0a5" />
    <path
      fill="#9ba0a5"
      d="M12 17a1 1 0 0 1-1-1v-2.54a1 1 0 0 1 2 0V16a1 1 0 0 1-1 1z"
    />
  </svg>
);
export default SvgVertexAi;
